<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    :title="getTitle"
    width="90%"
    showFooter
    :canFullscreen="true"
    :maskClosable="false"
    :keyboard="false"
    class="business-form-drawer"
    @ok="handleSubmit"
  >
    <template #footer>
      <a-space>
        <a-button @click="closeDrawer">
          <template #icon><CloseOutlined /></template>
          取消
        </a-button>
        <a-button type="primary" @click="handleSubmit" :loading="loading">
          <template #icon><SaveOutlined /></template>
          {{ isUpdate ? '更新' : '保存' }}
        </a-button>
      </a-space>
    </template>

    <div class="business-form-container">
      <!-- 顶部概览卡片 -->
      <a-card :bordered="false" class="overview-card mb-4" v-if="isUpdate">
        <template #title>
          <div class="card-title">
            <EditOutlined class="title-icon" />
            编辑商机  【{{ businessInfo?.businessNo || '-' }}】{{ businessInfo?.projectName || '-' }}
          </div>
        </template>
       
      </a-card>

      <!-- 新增商机的欢迎卡片 -->
      <a-card :bordered="false" class="welcome-card mb-4" v-else>
        <template #title>
          <div class="card-title">
            <PlusOutlined class="title-icon" />
            新增商机
          </div>
        </template>
        <div class="welcome-content">
          <div class="welcome-text">
            <h3>创建新的商机项目</h3>
            <p>请填写完整的商机信息，系统将自动生成项目编号并进行数据验证。</p>
          </div>
        </div>
      </a-card>

      <!-- 标签页 -->
      <a-tabs
        v-model:activeKey="activeTab"
        type="card"
        class="business-form-tabs"
        :tabBarGutter="8"
      >
        <!-- 基本信息标签页 -->
        <a-tab-pane key="basic" class="tab-pane-content">
          <template #tab>
            <span class="tab-title">
              <InfoCircleOutlined class="tab-icon" />
              基本信息
            </span>
          </template>

          <div class="alert-box" v-if="projectLevelInfo">
            <a-alert :message="projectLevelInfo" type="success" show-icon />
          </div>

          <div class="form-section">
            <BasicForm @register="registerBasicForm">
              <template #custId="{ model, field }">
                <CustomerSelect v-model:value="model[field]" placeholder="请选择客户单位" />
              </template>
              <template #projectLeader="{ model, field }">
                <UserSelect v-model:value="model[field]" placeholder="请选择项目负责人" />
              </template>
              <template #deptId="{ model, field }">
                <DeptSelect v-model:value="model[field]" placeholder="请选择所属分部" />
              </template>
              <template #yfDeptId="{ model, field }">
                <DeptSelect v-model:value="model[field]" placeholder="请选择研发分部" />
              </template>
              <template #projectContent="{ model, field }">
                <a-textarea
                  v-model:value="model[field]"
                  placeholder="请输入项目简介"
                  :rows="4"
                  :maxlength="2000"
                  show-count
                  class="enhanced-textarea"
                />
              </template>
              <template #lastNote="{ model, field }">
                <a-textarea
                  v-model:value="model[field]"
                  placeholder="请输入最后跟踪记录"
                  :rows="4"
                  :maxlength="2000"
                  show-count
                  class="enhanced-textarea"
                />
              </template>
            </BasicForm>
          </div>
        </a-tab-pane>

        <!-- 财务信息标签页 -->
        <a-tab-pane key="finance" class="tab-pane-content">
          <template #tab>
            <span class="tab-title">
              <DollarOutlined class="tab-icon" />
              财务信息
            </span>
          </template>

          <div class="alert-box" v-if="isHighLevelProject">
            <a-alert message="A+/A类项目的财务信息需要特别关注，请确保数据准确性。" type="warning" show-icon />
          </div>

          <div class="form-section">
            <BasicForm @register="registerFinanceForm" />
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </BasicDrawer>
</template>

<script lang="ts" setup>
  import { computed, ref, unref, nextTick } from 'vue';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasicForm, useForm } from '/@/components/Form';
  import { FormSchema } from '/@/components/Form/src/types/form';
  import { createBusiness, updateBusiness, BusinessModel, getBusinessInfo } from '/@/api/project/business';
  import CustomerSelect from '/@/views/project/components/CustomerSelect.vue';
  import UserSelect from '/@/components/Xh/Organize/src/UserSelect.vue';
  import DeptSelect from '/@/components/Xh/Organize/src/DepSelect.vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import {
    EditOutlined,
    PlusOutlined,
    SaveOutlined,
    CloseOutlined,
    InfoCircleOutlined,
    DollarOutlined,
    TeamOutlined,
    UserOutlined,
    DollarCircleOutlined,
    MoneyCollectOutlined,
  } from '@ant-design/icons-vue';

  const { createMessage } = useMessage();
  const emit = defineEmits(['register', 'reload']);
  const isUpdate = ref(false);
  const isCopy = ref(false);
  const businessId = ref('');
  const businessType = ref('建设商机'); // 默认为建设商机
  const activeTab = ref('basic');
  const businessInfo = ref<Partial<BusinessModel>>({});
  const loading = ref(false);

  // 项目等级信息提示
  const projectLevelInfo = computed(() => {
    const level = businessInfo.value?.projectLevel;
    if (level === 'A+') {
      return '项目等级为A+,表示2024年必须落地的重点项目，需要重点关注并及时更新进展。';
    } else if (level === 'A') {
      return '项目等级为A,表示2024年上半年落地，需要填写预计签署和合同分配信息等数据。';
    } else if (level === 'B') {
      return '项目等级为B,表示2024年下半年落地，需要填写预计签署和合同分配信息等数据。';
    }
    return '';
  });

  // 是否为高级别项目(A+或A)
  const isHighLevelProject = computed(() => {
    const level = businessInfo.value?.projectLevel;
    return level === 'A+' || level === 'A';
  });

  // 获取项目等级对应的颜色
  function getLevelColor(level: string) {
    switch (level) {
      case 'A+':
        return 'red';
      case 'A':
        return 'orange';
      case 'B':
        return 'blue';
      case 'C':
        return 'green';
      case 'D':
        return 'gray';
      default:
        return 'default';
    }
  }

  // 获取商机状态对应的颜色
  function getStatusColor(status: string) {
    switch (status) {
      case '跟踪中':
        return 'blue';
      case '方案报价中':
        return 'cyan';
      case '商务谈判中':
        return 'orange';
      case '已签':
        return 'green';
      case '已废弃':
        return 'red';
      case '明年跟踪':
        return 'purple';
      default:
        return 'default';
    }
  }

  // 格式化金额
  function formatAmount(amount: number | undefined) {
    return amount ? `${amount.toLocaleString('zh-CN')}万元` : '¥0.00';
  }

  // 获取标题
  const getTitle = computed(() => {
    return isUpdate.value ? '编辑商机' : '新增商机';
  });

  // 表单配置
  const formSchemas = computed((): FormSchema[] => {
    const baseSchemas: FormSchema[] = [
      // 项目信息分组
      {
        field: 'divider0',
        label: '项目信息',
        component: 'Divider',
        colProps: { span: 24 },
      },
      {
        field: 'projectName',
        label: '项目名称',
        component: 'Input',
        required: true,
        componentProps: { placeholder: '请输入项目名称', maxlength: 200 },
        rules: [
          { required: true, trigger: 'blur', message: '请输入项目名称' },
          { max: 200, message: '项目名称最多为200个字符', trigger: 'blur' },
        ],
        colProps: { span: 12 },
      },
      {
        field: 'businessNo',
        label: '项目编号',
        component: 'Input',
        componentProps: { placeholder: '系统自动生成', readonly: true },
        colProps: { span: 12 },
      },
      {
        field: 'projectLevel',
        label: '项目等级',
        component: 'Select',
        required: true,
        componentProps: {
          placeholder: '请选择项目等级',
          options: [
            { label: 'A', value: 'A' },
            { label: 'B', value: 'B' },
            { label: 'C', value: 'C' },
            { label: 'D', value: 'D' },
          ],
          onChange: (value: string) => {
            // 当项目等级变化时，更新提示信息
            if (value === 'A' || value === 'B') {
              // 显示财务信息标签页的警告
              activeTab.value = 'finance';
            }
          },
        },
        rules: [{ required: true, trigger: 'change', message: '请选择项目等级' }],
        colProps: { span: 12 },
      },
      {
        field: 'custId',
        label: '客户单位',
        component: 'Input',
        slot: 'custId',
        colProps: { span: 12 },
      },
      {
        field: 'marketLinkman',
        label: '市场负责人',
        component: 'Input',
        componentProps: { placeholder: '请输入市场负责人' },
        colProps: { span: 12 },
      },
      {
        field: 'presaleLinkman',
        label: '售前负责人',
        component: 'Input',
        componentProps: { placeholder: '请输入售前负责人' },
        colProps: { span: 12 },
      },
      {
        field: 'projectLeader',
        label: '项目负责人',
        component: 'Input',
        required: true,
        slot: 'projectLeader',
        rules: [{ required: true, trigger: 'change', message: '请选择项目负责人' }],
        colProps: { span: 12 },
      },
      {
        field: 'deptId',
        label: '所属分部',
        component: 'Input',
        slot: 'deptId',
        colProps: { span: 12 },
      },
      {
        field: 'businessTag',
        label: '商机标签',
        component: 'Select',
        componentProps: {
          options: [
            { label: '可靠项目', value: '可靠项目' },
            { label: '竞争项目', value: '竞争项目' },
            { label: '暂无经费', value: '暂无经费' },
          ],
        },
        colProps: { span: 24 },
      },
      {
        field: 'status',
        label: '商机状态',
        component: 'Select',
        defaultValue: '跟踪中',
        componentProps: {
          placeholder: '请选择商机状态',
          options: [
            { fullName: '跟踪中', id: '跟踪中' },
            { fullName: '方案报价中', id: '方案报价中' },
            { fullName: '商务谈判中', id: '商务谈判中' },
            { fullName: '已签', id: '已签' },
            { fullName: '已废弃', id: '已废弃' },
            { fullName: '明年跟踪', id: '明年跟踪' },
          ],
        },
        colProps: { span: 12 },
      },
      // 合同金额分组
      {
        field: 'divider1',
        label: '合同金额',
        component: 'Divider',
        colProps: { span: 24 },
      },
      {
        field: 'deptMoney',
        label: '软件部金额',
        component: 'InputNumber',
        required: true,
        componentProps: {
          placeholder: '请输入软件部金额',
          min: 0,
          precision: 2,
          style: 'width: 100%',
          addonAfter: '万元',
        },
        rules: [{ required: true, trigger: 'blur', message: '请输入软件部金额' }],
        colProps: { span: 12 },
      },
      {
        field: 'purchaseMoney',
        label: '外采金额',
        component: 'InputNumber',
        required: true,
        componentProps: {
          placeholder: '请输入外采金额',
          min: 0,
          precision: 2,
          style: 'width: 100%',
          addonAfter: '万元',
        },
        rules: [{ required: true, trigger: 'blur', message: '请输入外采金额' }],
        colProps: { span: 12 },
      },
      {
        field: 'profitMoney',
        label: '毛利',
        component: 'InputNumber',
        required: true,
        componentProps: {
          placeholder: '请输入毛利',
          min: 0,
          precision: 2,
          style: 'width: 100%',
          addonAfter: '万元',
        },
        rules: [{ required: true, trigger: 'blur', message: '请输入毛利' }],
        colProps: { span: 12 },
      },
      {
        field: 'projectType',
        label: '项目类型',
        component: 'Select',
        required: true,
        componentProps: {
          placeholder: '请选择项目类型',
          options: [
            { label: '建设', value: '建设' },
            { label: '维护', value: '维护' },
          ],
        },
        rules: [{ required: true, trigger: 'change', message: '请选择项目类型' }],
        colProps: { span: 12 },
      },
      {
        field: 'projectContent',
        label: '项目简介',
        component: 'Input',
        slot: 'projectContent',
        componentProps: { placeholder: '请输入项目简介', maxlength: 2000, showCount: true, rows: 3, type: 'textarea' },
        rules: [{ max: 2000, message: '项目简介最多为2000个字符', trigger: 'blur' }],
        colProps: { span: 24 },
      },
      {
        field: 'evaSignMonth',
        label: '预计落地月份',
        component: 'MonthPicker',
        componentProps: {
          placeholder: '请选择预计落地月份',
          style: 'width: 100%',
          valueFormat: 'YYYY-MM',
        },
        colProps: { span: 12 },
      },
      {
        field: 'checkDate',
        label: '合同审核日期',
        component: 'DatePicker',
        componentProps: { placeholder: '请选择合同审核日期', style: 'width: 100%' },
        colProps: { span: 12 },
      },
      {
        field: 'evaFirstMonth',
        label: '预计首比回款时间',
        component: 'DatePicker',
        componentProps: { placeholder: '请选择预计首比回款时间', style: 'width: 100%' },
        colProps: { span: 12 },
      },
      {
        field: 'evaFirstAmount',
        label: '预计首比回款金额(万元)',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入预计首比回款金额',
          min: 0,
          precision: 2,
          style: 'width: 100%',
          addonAfter: '万元',
        },
        colProps: { span: 12 },
      },
      {
        field: 'evaSecondMonth',
        label: '第二笔回款时间',
        component: 'DatePicker',
        componentProps: { placeholder: '请选择第二笔回款时间', style: 'width: 100%' },
        colProps: { span: 12 },
      },
      {
        field: 'evaSecondAmount',
        label: '二笔回款金额(万元)',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入二笔回款金额',
          min: 0,
          precision: 2,
          style: 'width: 100%',
          addonAfter: '万元',
        },
        colProps: { span: 12 },
      },
      // 外采信息分组
      {
        field: 'divider2',
        label: '外采信息',
        component: 'Divider',
        colProps: { span: 24 },
      },
      {
        field: 'evaFirstexternalMonth',
        label: '首次外采月份',
        component: 'MonthPicker',
        componentProps: {
          placeholder: '请选择首次外采月份',
          style: 'width: 100%',
          valueFormat: 'YYYY-MM',
        },
        colProps: { span: 12 },
      },
      {
        field: 'evaFirstexternalAmount',
        label: '首次外采金额',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入首次外采金额',
          min: 0,
          precision: 2,
          style: 'width: 100%',
          addonAfter: '万元',
        },
        colProps: { span: 12 },
      },
      {
        field: 'evaSecondexternalMonth',
        label: '二次外采月份',
        component: 'MonthPicker',
        componentProps: {
          placeholder: '请选择二次外采月份',
          style: 'width: 100%',
          valueFormat: 'YYYY-MM',
        },
        colProps: { span: 12 },
      },
      {
        field: 'evaSecondexternalAmount',
        label: '二次外采金额',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入二次外采金额',
          min: 0,
          precision: 2,
          style: 'width: 100%',
          addonAfter: '万元',
        },
        colProps: { span: 12 },
      },
      // 部门分配信息
      {
        field: 'divider3',
        label: '部门分配',
        component: 'Divider',
        colProps: { span: 24 },
      },
      {
        field: 'yfYbAmount',
        label: '一部金额',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入一部金额',
          min: 0,
          precision: 2,
          style: 'width: 100%',
          addonAfter: '万元',
        },
        colProps: { span: 12 },
      },
      {
        field: 'outYbAmount',
        label: '一部外采',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入一部外采',
          min: 0,
          precision: 2,
          style: 'width: 100%',
          addonAfter: '万元',
        },
        colProps: { span: 12 },
      },
      {
        field: 'yfEbAmount',
        label: '二部金额',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入二部金额',
          min: 0,
          precision: 2,
          style: 'width: 100%',
          addonAfter: '万元',
        },
        colProps: { span: 12 },
      },
      {
        field: 'outEbAmount',
        label: '二部外采',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入二部外采',
          min: 0,
          precision: 2,
          style: 'width: 100%',
          addonAfter: '万元',
        },
        colProps: { span: 12 },
      },
    ];

    return baseSchemas;
  });

  // 基本信息表单字段
  const basicFormFields = [
    'divider0',
    'projectName',
    'businessNo',
    'projectLevel',
    'custId',
    'marketLinkman',
    'presaleLinkman',
    'projectLeader',
    'deptId',
    'businessTag',
    'status',
    'divider1',
    'deptMoney',
    'purchaseMoney',
    'profitMoney',
    'projectType',
    'projectContent',
    'evaSignMonth',
    'checkDate',
    'evaFirstMonth',
    'evaFirstAmount',
    'evaSecondMonth',
    'evaSecondAmount',
  ];

  // 财务信息表单字段
  const financeFormFields = [
    'divider2',
    'evaFirstexternalMonth',
    'evaFirstexternalAmount',
    'evaSecondexternalMonth',
    'evaSecondexternalAmount',
    'divider3',
    'yfYbAmount',
    'outYbAmount',
    'yfEbAmount',
    'outEbAmount',
  ];

  // 注册基本信息表单
  const [registerBasicForm, { resetFields: resetBasicFields, setFieldsValue: setBasicFieldsValue, validate: validateBasicForm }] = useForm({
    labelWidth: 130,
    schemas: formSchemas.value.filter(schema => basicFormFields.includes(schema.field)),
    showActionButtonGroup: false,
    baseColProps: { span: 12 },
  });

  // 注册财务信息表单
  const [registerFinanceForm, { resetFields: resetFinanceFields, setFieldsValue: setFinanceFieldsValue, validate: validateFinanceForm }] = useForm({
    labelWidth: 130,
    schemas: formSchemas.value.filter(schema => financeFormFields.includes(schema.field)),
    showActionButtonGroup: false,
    baseColProps: { span: 12 },
  });

  // 注册抽屉
  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async data => {
    resetBasicFields();
    resetFinanceFields();
    setDrawerProps({ confirmLoading: false });
    activeTab.value = 'basic';

    isUpdate.value = !!data?.isUpdate;
    isCopy.value = !!data?.copyData;

    if (unref(isUpdate)) {
      businessId.value = data.record.id;
      businessType.value = data.record.projType === '提前项目' ? '建设商机' : '维护商机';

      // 获取商机详情
      try {
        const businessDetailRes = await getBusinessInfo(businessId.value);
        const businessDetail = businessDetailRes.data;
        console.log('获取到的商机详情:', businessDetail);

        businessInfo.value = businessDetail;

        // 设置表单值
        const formData = {
          ...businessDetail,
          projType: businessType.value,
        };

        console.log('设置的表单数据:', formData);
        setBasicFieldsValue(formData);
        setFinanceFieldsValue(formData);
      } catch (error) {
        console.error('获取商机详情失败:', error);
        createMessage.error('获取商机详情失败');
      }
    } else if (data?.copyData) {
      // 复制模式
      console.log('复制模式，预填充数据:', data.copyData);

      businessType.value = data.copyData.projType === '维护项目' ? '维护商机' : '建设商机';
      businessInfo.value = data.copyData;

      // 转换商机类型显示
      const formData = {
        ...data.copyData,
        projType: businessType.value,
      };

      console.log('设置复制的表单数据:', formData);

      // 设置表单值
      setBasicFieldsValue(formData);
      setFinanceFieldsValue(formData);
    } else {
      businessType.value = '建设商机';
      businessInfo.value = {};
      isCopy.value = false;
    }
  });



  // 提交表单
  async function handleSubmit() {
    try {
      // 验证基本信息表单
      const basicValues = await validateBasicForm();

      // 验证财务信息表单
      const financeValues = await validateFinanceForm();

      // 合并表单数据
      const values = {
        ...basicValues,
        ...financeValues,
      };

      setDrawerProps({ confirmLoading: true });

      // 转换商机类型
      values.projType = values.projType === '建设商机' ? '常规项目' : '维护项目';

      if (unref(isUpdate)) {
        await updateBusiness(businessId.value, values);
        createMessage.success('更新成功');
      } else {
        await createBusiness(values);
        createMessage.success('新增成功');
      }

      closeDrawer();
      emit('reload');
    } catch (error) {
      console.error('表单验证失败或提交出错:', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>

<style lang="less" scoped>
  .business-form-drawer {
    :deep(.ant-drawer-body) {
      padding: 0;
      background: #f5f7fa;
    }

    :deep(.ant-drawer-header) {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-bottom: none;

      .ant-drawer-title {
        color: #fff;
        font-weight: 600;
        font-size: 16px;
      }

      .ant-drawer-close {
        color: rgba(255, 255, 255, 0.8);

        &:hover {
          color: #fff;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
        }
      }
    }

    :deep(.ant-drawer-footer) {
      background: #fff;
      border-top: 1px solid #e8eaec;
      padding: 16px 24px;
    }
  }

  .business-form-container {
    height: 100%;
    padding: 24px;
    overflow-y: auto;
  }

  .overview-card,
  .welcome-card {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8eaec;

    :deep(.ant-card-head) {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-bottom: 1px solid #e8eaec;
      border-radius: 12px 12px 0 0;
      padding: 16px 24px;

      .ant-card-head-title {
        padding: 0;
        font-size: 18px;
        font-weight: 600;
      }
    }

    :deep(.ant-card-body) {
      padding: 24px;
    }
  }

  .overview-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;

    .overview-item {
      .overview-label {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
        font-weight: 500;

        .label-icon {
          margin-right: 6px;
          font-size: 16px;
          color: #1890ff;
        }
      }

      .overview-value {
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;

        &.amount {
          font-size: 18px;
          color: #1890ff;
          font-weight: 700;

          &.external {
            color: #fa8c16;
          }
        }
      }
    }
  }

  .welcome-content {
    .welcome-text {
      text-align: center;
      padding: 20px 0;

      h3 {
        color: #2c3e50;
        margin-bottom: 12px;
        font-size: 20px;
        font-weight: 600;
      }

      p {
        color: #666;
        font-size: 14px;
        line-height: 1.6;
        margin: 0;
      }
    }
  }

  .card-title {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #2c3e50;

    .title-icon {
      font-size: 18px;
      color: #1890ff;
    }
  }

  .alert-box {
    margin-bottom: 24px;

    :deep(.ant-alert) {
      border-radius: 8px;
      border: none;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    :deep(.ant-alert-success) {
      background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
      border-left: 4px solid #52c41a;
    }

    :deep(.ant-alert-warning) {
      background: linear-gradient(135deg, #fffbe6 0%, #fff7e6 100%);
      border-left: 4px solid #faad14;
    }
  }

  .business-form-tabs {
    height: calc(100% - 200px);

    :deep(.ant-tabs-nav) {
      background: #fff;
      margin: 0;
      padding: 16px 24px 0;
      border-bottom: 1px solid #e8eaec;
      border-radius: 12px 12px 0 0;

      .ant-tabs-tab {
        border: 1px solid #e8eaec;
        border-radius: 8px 8px 0 0;
        margin-right: 8px;
        padding: 12px 20px;
        background: #fafbfc;
        transition: all 0.3s ease;

        &:hover {
          background: #f0f2f5;
          border-color: #d9d9d9;
        }

        &.ant-tabs-tab-active {
          background: #fff;
          border-color: #1890ff;
          border-bottom-color: #fff;

          .tab-title {
            color: #1890ff;
            font-weight: 600;
          }
        }
      }
    }

    :deep(.ant-tabs-content-holder) {
      background: #f5f7fa;
      padding: 0;
      height: calc(100% - 60px);
      overflow-y: auto;
    }

    :deep(.ant-tabs-tabpane) {
      height: 100%;
      padding: 0;
    }
  }

  .tab-pane-content {
    padding: 24px;
    height: 100%;
    overflow-y: auto;
  }

  .tab-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #666;
    transition: color 0.3s ease;

    .tab-icon {
      font-size: 16px;
    }
  }

  .form-section {
    background: #fff;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e8eaec;
  }

  :deep(.ant-form) {
    .ant-form-item-label > label {
      font-weight: 500;
      color: #2c3e50;
    }

    .ant-divider {
      margin: 24px 0 16px 0;
      border-color: #e8eaec;

      .ant-divider-inner-text {
        font-weight: 600;
        color: #1890ff;
        background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
        padding: 8px 16px;
        border-radius: 6px;
        border: 1px solid #b7eb8f;
      }
    }

    .ant-input,
    .ant-input-number,
    .ant-select-selector,
    .ant-picker {
      border-radius: 6px;
      border: 1px solid #d9d9d9;
      transition: all 0.3s ease;

      &:hover {
        border-color: #40a9ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
      }

      &:focus,
      &.ant-input-focused,
      &.ant-select-focused .ant-select-selector,
      &.ant-picker-focused {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }

    .ant-input-number-group-addon {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-color: #d9d9d9;
      color: #666;
      font-weight: 500;
    }

    .ant-select {
      .ant-select-selection-item {
        color: #2c3e50;
        font-weight: 500;
      }
    }
  }

  .enhanced-textarea {
    border-radius: 8px;
    border: 1px solid #d9d9d9;
    transition: all 0.3s ease;

    &:hover {
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
    }

    &:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }

  .mb-4 {
    margin-bottom: 24px;
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .business-form-drawer {
      :deep(.ant-drawer) {
        width: 95% !important;
      }
    }

    .business-form-container {
      padding: 16px;
    }

    .overview-content {
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 16px;
    }

    .tab-pane-content {
      padding: 16px;
    }

    .form-section {
      padding: 20px 16px;
    }
  }

  @media (max-width: 768px) {
    .business-form-tabs {
      :deep(.ant-tabs-nav) {
        padding: 12px 16px 0;

        .ant-tabs-tab {
          padding: 8px 12px;
          margin-right: 4px;

          .tab-title {
            font-size: 12px;

            .tab-icon {
              font-size: 14px;
            }
          }
        }
      }
    }

    .tab-pane-content {
      padding: 12px;
    }

    .overview-content {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    .form-section {
      border-radius: 8px;
      padding: 16px 12px;
    }

    .card-title {
      .title-icon {
        font-size: 16px;
      }
    }
  }

  // 滚动条美化
  .business-form-container,
  .tab-pane-content {
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  // 标签样式
  :deep(.ant-tag) {
    border-radius: 6px;
    padding: 4px 12px;
    font-weight: 500;
    border: none;
  }

  // 响应式设计
  @media (max-width: 768px) {
    .business-info-card {
      :deep(.ant-descriptions) {
        .ant-descriptions-item {
          padding-bottom: 12px;
        }
      }
    }

    :deep(.ant-tabs) {
      .ant-tabs-nav {
        .ant-tabs-tab {
          padding: 8px 12px;
          margin-right: 4px;
          font-size: 14px;
        }
      }

      .ant-tabs-content-holder {
        padding: 16px;
      }
    }
  }
</style>
