package com.xinghuo.project.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目商机实体类
 */
@Data
@EqualsAndHashCode
@TableName("zz_proj_business")
public class ProjBusinessEntity {

    /**
     * ID
     */
    @TableId("id")
    private String id;

    /**
     * 项目类型 1-建设商机，2-维护商机，3-提前项目
     */
    @TableField("proj_type")
    private Integer projType;

    /**
     * 项目编号
     */
    @TableField("business_no")
    private String businessNo;

    /**
     * 项目等级
     */
    @TableField("project_level")
    private String projectLevel;

    /**
     * 项目名称
     */
    @TableField("project_name")
    private String projectName;

    /**
     * 项目简介
     */
    @TableField("project_content")
    private String projectContent;

    /**
     * 商机标签
     */
    @TableField("business_tag")
    private String businessTag;

    /**
     * 客户单位ID
     */
    @TableField("cust_id")
    private String custId;

    /**
     * 市场负责人
     */
    @TableField("market_linkman")
    private String marketLinkman;

    /**
     * 售前负责人
     */
    @TableField("presale_linkman")
    private String presaleLinkman;

    /**
     * 项目负责人
     */
    @TableField("project_leader")
    private String projectLeader;

    /**
     * 项目类型
     */
    @TableField("project_type")
    private String projectType;

    /**
     * 所属分部
     */
    @TableField("dept_id")
    private String deptId;

    /**
     * 研发分部
     */
    @TableField("yf_dept_id")
    private String yfDeptId;

    /**
     * 启动日期
     */
    @TableField("start_date")
    private Date startDate;

    /**
     * 预计落地日期
     */
    @TableField("eva_sign_month")
    private Date evaSignMonth;

    /**
     * 预计首比回款时间
     */
    @TableField("eva_first_month")
    private Date evaFirstMonth;

    /**
     * 预计首比回款金额(万元)
     */
    @TableField("eva_first_amount")
    private BigDecimal evaFirstAmount;

    /**
     * 第二笔回款时间
     */
    @TableField("eva_second_month")
    private Date evaSecondMonth;

    /**
     * 二笔回款金额(万元)
     */
    @TableField("eva_second_amount")
    private BigDecimal evaSecondAmount;

    /**
     * 首次外采月份
     */
    @TableField("eva_firstexternal_month")
    private Date evaFirstexternalMonth;

    /**
     * 首次外采金额
     */
    @TableField("eva_firstexternal_amount")
    private BigDecimal evaFirstexternalAmount;

    /**
     * 二次外采月份
     */
    @TableField("eva_secondexternal_month")
    private Date evaSecondexternalMonth;

    /**
     * 二次外采金额
     */
    @TableField("eva_secondexternal_amount")
    private BigDecimal evaSecondexternalAmount;

    /**
     * 合同审核日期
     */
    @TableField("check_date")
    private Date checkDate;

    /**
     * 交底日期
     */
    @TableField("trans_date")
    private Date transDate;

    /**
     * 今年收款比例
     */
    @TableField("year_money_ratio")
    private String yearMoneyRatio;

    /**
     * 今年收款
     */
    @TableField("year_money")
    private BigDecimal yearMoney;

    /**
     * 软件部金额
     */
    @TableField("dept_money")
    private BigDecimal deptMoney;

    /**
     * 一部金额
     */
    @TableField("yf_yb_amount")
    private BigDecimal yfYbAmount;

    /**
     * 二部金额
     */
    @TableField("yf_eb_amount")
    private BigDecimal yfEbAmount;

    /**
     * 交付金额
     */
    @TableField("yf_jf_amount")
    private BigDecimal yfJfAmount;
    /**
     * 综合金额
     */
    @TableField("yf_other_amount")
    private BigDecimal yfOtherAmount;

    /**
     * 一部外采
     */
    @TableField("out_yb_amount")
    private BigDecimal outYbAmount;

    /**
     * 二部外采
     */
    @TableField("out_eb_amount")
    private BigDecimal outEbAmount;

    /**
     * 交付外采
     */
    @TableField("out_jf_amount")
    private BigDecimal outJfAmount;

    /**
     * 综合外采
     */
    @TableField("out_other_amount")
    private BigDecimal outOtherAmount;

    /**
     * 外采金额
     */
    @TableField("purchase_money")
    private BigDecimal purchaseMoney;

    /**
     * 毛利
     */
    @TableField("profit_money")
    private BigDecimal profitMoney;

    /**
     * 累计工时(自动计算)
     */
    @TableField("auto_manhours")
    private BigDecimal autoManhours;

    /**
     * 是否部署系统
     */
    @TableField("deploy_status")
    private String deployStatus;

    /**
     * 备注
     */
    @TableField("note")
    private String note;

    /**
     * 最后跟踪记录
     */
    @TableField("last_note")
    private String lastNote;

    /**
     * 项目跟踪状态 1-跟踪中 2-已签，3-已废弃 4-24年跟踪
     */
    @TableField("status")
    private String status;

    /**
     * 签订合同编号
     */
    @TableField("project_no")
    private String projectNo;

    /**
     * 逻辑删除字段
     */
    @TableField("is_deleted")
    private String isDeleted;

    /**
     * 删除标志
     */
    @TableField("F_DeleteMark")
    private Integer deleteMark;

    /**
     * 创建用户
     */
    @TableField("create_user_id")
    private String createUserId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 最后修改人
     */
    @TableField("last_modified_user_id")
    private String lastModifiedUserId;

    /**
     * 最后更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 租户ID
     */
    @TableField("f_tenantid")
    private String tenantId;

    /**
     * 流程ID
     */
    @TableField("f_flowid")
    private String flowId;

    /**
     * 预计结束日期
     */
    @TableField("yj_end_date")
    private Date yjEndDate;

    /**
     * 预计开始日期
     */
    @TableField("yj_start_date")
    private Date yjStartDate;

    /**
     * 工时填写状态 1-可填写 0-已结束
     */
    @TableField("work_status")
    private Integer workStatus;
}
