@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=G:\v2\pd-xace-v2\xace-web\xh-web-vue3\node_modules\.pnpm\jsbarcode@3.11.5\node_modules\jsbarcode\bin\barcodes\CODE128\node_modules;G:\v2\pd-xace-v2\xace-web\xh-web-vue3\node_modules\.pnpm\jsbarcode@3.11.5\node_modules\jsbarcode\bin\barcodes\node_modules;G:\v2\pd-xace-v2\xace-web\xh-web-vue3\node_modules\.pnpm\jsbarcode@3.11.5\node_modules\jsbarcode\bin\node_modules;G:\v2\pd-xace-v2\xace-web\xh-web-vue3\node_modules\.pnpm\jsbarcode@3.11.5\node_modules\jsbarcode\node_modules;G:\v2\pd-xace-v2\xace-web\xh-web-vue3\node_modules\.pnpm\jsbarcode@3.11.5\node_modules;G:\v2\pd-xace-v2\xace-web\xh-web-vue3\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=G:\v2\pd-xace-v2\xace-web\xh-web-vue3\node_modules\.pnpm\jsbarcode@3.11.5\node_modules\jsbarcode\bin\barcodes\CODE128\node_modules;G:\v2\pd-xace-v2\xace-web\xh-web-vue3\node_modules\.pnpm\jsbarcode@3.11.5\node_modules\jsbarcode\bin\barcodes\node_modules;G:\v2\pd-xace-v2\xace-web\xh-web-vue3\node_modules\.pnpm\jsbarcode@3.11.5\node_modules\jsbarcode\bin\node_modules;G:\v2\pd-xace-v2\xace-web\xh-web-vue3\node_modules\.pnpm\jsbarcode@3.11.5\node_modules\jsbarcode\node_modules;G:\v2\pd-xace-v2\xace-web\xh-web-vue3\node_modules\.pnpm\jsbarcode@3.11.5\node_modules;G:\v2\pd-xace-v2\xace-web\xh-web-vue3\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\jsbarcode\bin\barcodes\CODE128\constants.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\jsbarcode\bin\barcodes\CODE128\constants.js" %*
)
