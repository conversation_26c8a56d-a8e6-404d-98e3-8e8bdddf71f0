<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    :title="getTitle"
    width="90%"
    showFooter
    :canFullscreen="true"
    :maskClosable="false"
    :keyboard="false"
    class="business-form-drawer"
    @ok="handleSubmit"
  >
    <template #footer>
      <a-space>
        <a-button @click="closeDrawer">
          <template #icon><CloseOutlined /></template>
          取消
        </a-button>
        <a-button type="primary" @click="handleSubmit" :loading="loading">
          <template #icon><SaveOutlined /></template>
          {{ isUpdate ? '更新' : '保存' }}
        </a-button>
      </a-space>
    </template>

    <div class="business-form-container">
      <!-- 顶部概览卡片 -->
      <a-card :bordered="false" class="overview-card mb-4" v-if="isUpdate">
        <template #title>
          <div class="card-title">
            <EditOutlined class="title-icon" />
            编辑商机  【{{ businessInfo?.businessNo || '-' }}】{{ businessInfo?.projectName || '-' }}
          </div>
        </template>
      </a-card>

      <!-- 新增商机的提示卡片 -->
      <a-card :bordered="false" class="welcome-card mb-4" v-else>
        <div class="welcome-content">
          <div class="welcome-text">
            <InfoCircleOutlined class="info-icon" />
            <span>项目等级为A,表示2025年上半年落地，B类下半年落地，需要填写预计签署和合同分配信息等数据。</span>
          </div>
        </div>
      </a-card>

      <!-- 左右分栏布局 -->
      <a-layout class="form-layout">
        <!-- 左侧导航 -->
        <a-layout-sider
          width="240"
          class="navigation-sider"
          :breakpoint="'lg'"
          :collapsed-width="0"
          v-model:collapsed="siderCollapsed"
        >
          <div class="navigation-container">
            <div class="navigation-header">
              <h4>快速导航</h4>
            </div>
            <a-menu
              v-model:selectedKeys="selectedNavKeys"
              mode="inline"
              class="navigation-menu"
              @click="handleNavClick"
            >
              <a-menu-item key="project">
                <template #icon><InfoCircleOutlined /></template>
                项目信息
              </a-menu-item>
              <a-menu-item key="contract">
                <template #icon><DollarOutlined /></template>
                合同信息
              </a-menu-item>
              <a-menu-item key="outsource">
                <template #icon><ShoppingCartOutlined /></template>
                项目外采
              </a-menu-item>
              <a-menu-item key="allocation">
                <template #icon><TeamOutlined /></template>
                合同分配
              </a-menu-item>
            </a-menu>
          </div>
        </a-layout-sider>

        <!-- 右侧内容区域 -->
        <a-layout-content class="form-content">
          <!-- 滚动进度指示器 -->
          <div class="scroll-progress" ref="scrollProgress"></div>
          <div class="form-scroll-container" ref="scrollContainer" @scroll="updateScrollProgress">
            <!-- 项目信息模块 -->
            <div id="project-section" class="form-module" ref="projectSection">
              <a-card :bordered="false" class="module-card">
                <template #title>
                  <div class="module-title">
                    <InfoCircleOutlined class="module-icon" />
                    项目信息
                  </div>
                </template>

                <div class="alert-box" v-if="projectLevelInfo">
                  <a-alert :message="projectLevelInfo" type="success" show-icon />
                </div>

                <BasicForm @register="registerProjectForm">
                  <template #custId="{ model, field }">
                    <CustomerSelect v-model:value="model[field]" placeholder="请选择客户单位" />
                  </template>
                  <template #projectLeader="{ model, field }">
                    <UserSelect v-model:value="model[field]" placeholder="请选择项目负责人" />
                  </template>
                  <template #deptId="{ model, field }">
                    <DeptSelect v-model:value="model[field]" placeholder="请选择所属分部" />
                  </template>
                  <template #projectContent="{ model, field }">
                    <a-textarea
                      v-model:value="model[field]"
                      placeholder="请输入项目简介"
                      :rows="4"
                      :maxlength="2000"
                      show-count
                      class="enhanced-textarea"
                    />
                  </template>
                </BasicForm>
              </a-card>
            </div>

            <!-- 合同信息模块 -->
            <div id="contract-section" class="form-module" ref="contractSection">
              <a-card :bordered="false" class="module-card">
                <template #title>
                  <div class="module-title">
                    <DollarOutlined class="module-icon" />
                    合同信息
                  </div>
                </template>

                <div class="alert-box" v-if="isHighLevelProject">
                  <a-alert message="A+/A类项目的财务信息需要特别关注，请确保数据准确性。" type="warning" show-icon />
                </div>

                <BasicForm @register="registerContractForm" />
              </a-card>
            </div>

            <!-- 项目外采模块 -->
            <div id="outsource-section" class="form-module" ref="outsourceSection">
              <a-card :bordered="false" class="module-card">
                <template #title>
                  <div class="module-title">
                    <ShoppingCartOutlined class="module-icon" />
                    项目外采
                  </div>
                </template>

                <BasicForm @register="registerOutsourceForm" />
              </a-card>
            </div>

            <!-- 合同分配模块 -->
            <div id="allocation-section" class="form-module" ref="allocationSection">
              <a-card :bordered="false" class="module-card">
                <template #title>
                  <div class="module-title">
                    <TeamOutlined class="module-icon" />
                    合同分配
                  </div>
                </template>

                <BasicForm @register="registerAllocationForm" />
              </a-card>
            </div>
          </div>
        </a-layout-content>
      </a-layout>
    </div>
  </BasicDrawer>
</template>

<script lang="ts" setup>
  import { computed, ref, unref, nextTick, onMounted, onUnmounted } from 'vue';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasicForm, useForm } from '/@/components/Form';
  import { FormSchema } from '/@/components/Form/src/types/form';
  import { createBusiness, updateBusiness, BusinessModel, getBusinessInfo } from '/@/api/project/business';
  import CustomerSelect from '/@/views/project/components/CustomerSelect.vue';
  import UserSelect from '/@/components/Xh/Organize/src/UserSelect.vue';
  import DeptSelect from '/@/components/Xh/Organize/src/DepSelect.vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import {
    EditOutlined,
    PlusOutlined,
    SaveOutlined,
    CloseOutlined,
    InfoCircleOutlined,
    DollarOutlined,
    TeamOutlined,
    UserOutlined,
    DollarCircleOutlined,
    MoneyCollectOutlined,
    ShoppingCartOutlined,
  } from '@ant-design/icons-vue';

  const { createMessage } = useMessage();
  const emit = defineEmits(['register', 'reload']);
  const isUpdate = ref(false);
  const isCopy = ref(false);
  const businessId = ref('');
  const businessType = ref('建设商机'); // 默认为建设商机
  const businessInfo = ref<Partial<BusinessModel>>({});
  const loading = ref(false);

  // 导航相关状态
  const siderCollapsed = ref(false);
  const selectedNavKeys = ref(['project']);
  const scrollContainer = ref();
  const scrollProgress = ref();
  const projectSection = ref();
  const contractSection = ref();
  const outsourceSection = ref();
  const allocationSection = ref();

  // 项目等级信息提示
  const projectLevelInfo = computed(() => {
    const level = businessInfo.value?.projectLevel;
    if (level === 'A+') {
      return '项目等级为A+,表示当年必须落地的重点项目，需要重点关注并及时更新进展。';
    } else if (level === 'A') {
      return '项目等级为A,表示当年上半年落地，需要填写预计签署和合同分配信息等数据。';
    } else if (level === 'B') {
      return '项目等级为B,表示当年下半年落地，需要填写预计签署和合同分配信息等数据。';
    }
    return '';
  });

  // 是否为高级别项目(A+或A)
  const isHighLevelProject = computed(() => {
    const level = businessInfo.value?.projectLevel;
    return level === 'A+' || level === 'A';
  });

  // 获取项目等级对应的颜色
  function getLevelColor(level: string) {
    switch (level) {
      case 'A+':
        return 'red';
      case 'A':
        return 'orange';
      case 'B':
        return 'blue';
      case 'C':
        return 'green';
      case 'D':
        return 'gray';
      default:
        return 'default';
    }
  }

  // 获取商机状态对应的颜色
  function getStatusColor(status: string) {
    switch (status) {
      case '跟踪中':
        return 'blue';
      case '方案报价中':
        return 'cyan';
      case '商务谈判中':
        return 'orange';
      case '已签':
        return 'green';
      case '已废弃':
        return 'red';
      case '明年跟踪':
        return 'purple';
      default:
        return 'default';
    }
  }

  // 格式化金额
  function formatAmount(amount: number | undefined) {
    return amount ? `${amount.toLocaleString('zh-CN')}万元` : '¥0.00';
  }

  // 获取标题
  const getTitle = computed(() => {
    return isUpdate.value ? '编辑商机' : '新增商机';
  });

  // 导航点击处理
  function handleNavClick({ key }: { key: string }) {
    selectedNavKeys.value = [key];
    scrollToSection(key);
  }

  // 滚动到指定区域
  function scrollToSection(sectionKey: string) {
    const sectionMap = {
      project: projectSection.value,
      contract: contractSection.value,
      outsource: outsourceSection.value,
      allocation: allocationSection.value,
    };

    const targetSection = sectionMap[sectionKey];
    if (targetSection) {
      targetSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  }

  // 使用 IntersectionObserver 监听区域可见性
  let observer: IntersectionObserver | null = null;

  function setupIntersectionObserver() {
    if (!scrollContainer.value) return;

    // 清理之前的观察器
    if (observer) {
      observer.disconnect();
    }

    const options = {
      root: scrollContainer.value,
      rootMargin: '-20% 0px -60% 0px', // 当区域在视口上20%-40%区域时触发
      threshold: 0.1
    };

    observer = new IntersectionObserver(entries => {
      // 找到最靠近顶部的可见区域
      let topMostEntry: IntersectionObserverEntry | null = null;
      let topMostTop = Infinity;

      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const rect = entry.boundingClientRect;
          if (rect.top < topMostTop) {
            topMostTop = rect.top;
            topMostEntry = entry;
          }
        }
      });

      if (topMostEntry) {
        const sectionKey = (topMostEntry.target as HTMLElement).id.replace('-section', '');
        selectedNavKeys.value = [sectionKey];
      }
    }, options);

    // 观察所有区域
    const sections = [projectSection, contractSection, outsourceSection, allocationSection];
    sections.forEach(section => {
      if (section.value) {
        observer!.observe(section.value);
      }
    });
  }

  // 组件挂载时设置观察器
  onMounted(() => {
    nextTick(() => {
      setupIntersectionObserver();
    });
  });

  // 更新滚动进度
  function updateScrollProgress() {
    if (!scrollContainer.value || !scrollProgress.value) return;

    const { scrollTop, scrollHeight, clientHeight } = scrollContainer.value;
    const progress = (scrollTop / (scrollHeight - clientHeight)) * 100;
    scrollProgress.value.style.width = `${Math.min(progress, 100)}%`;
  }

  // 组件卸载时清理观察器
  onUnmounted(() => {
    if (observer) {
      observer.disconnect();
    }
  });

  // 表单配置
  const formSchemas = computed((): FormSchema[] => {
    const baseSchemas: FormSchema[] = [
      {
        field: 'projectName',
        label: '项目名称',
        component: 'Input',
        required: true,
        componentProps: { placeholder: '请输入项目名称', maxlength: 200 },
        rules: [
          { required: true, trigger: 'blur', message: '请输入项目名称' },
          { max: 200, message: '项目名称最多为200个字符', trigger: 'blur' },
        ],
        colProps: { span: 12 },
      },
      {
        field: 'businessNo',
        label: '项目编号',
        component: 'Input',
        componentProps: { placeholder: '系统自动生成', readonly: true },
        colProps: { span: 12 },
      },
      {
        field: 'projectLevel',
        label: '项目等级',
        component: 'Select',
        required: true,
        componentProps: {
          placeholder: '请选择项目等级',
          options: [
            { label: 'A', value: 'A' },
            { label: 'B', value: 'B' },
            { label: 'C', value: 'C' },
            { label: 'D', value: 'D' },
          ],
          onChange: (value: string) => {
            // 当项目等级变化时，更新提示信息
            if (value === 'A' || value === 'B') {
              // 显示合同信息模块的警告
              selectedNavKeys.value = ['contract'];
              scrollToSection('contract');
            }
          },
        },
        rules: [{ required: true, trigger: 'change', message: '请选择项目等级' }],
        colProps: { span: 12 },
      },
      {
        field: 'custId',
        label: '客户单位',
        component: 'Input',
        slot: 'custId',
        colProps: { span: 12 },
      },
      {
        field: 'marketLinkman',
        label: '市场负责人',
        component: 'Input',
        componentProps: { placeholder: '请输入市场负责人' },
        colProps: { span: 12 },
      },
      {
        field: 'presaleLinkman',
        label: '售前负责人',
        component: 'Input',
        componentProps: { placeholder: '请输入售前负责人' },
        colProps: { span: 12 },
      },
      {
        field: 'projectLeader',
        label: '项目负责人',
        component: 'Input',
        required: true,
        slot: 'projectLeader',
        rules: [{ required: true, trigger: 'change', message: '请选择项目负责人' }],
        colProps: { span: 12 },
      },
      {
        field: 'deptId',
        label: '所属分部',
        component: 'Input',
        slot: 'deptId',
        colProps: { span: 12 },
      },
      {
        field: 'businessTag',
        label: '商机标签',
        component: 'Select',
        componentProps: {
          options: [
            { label: '可靠项目', value: '可靠项目' },
            { label: '竞争项目', value: '竞争项目' },
            { label: '暂无经费', value: '暂无经费' },
          ],
        },
        colProps: { span: 24 },
      },
      {
        field: 'status',
        label: '商机状态',
        component: 'Select',
        defaultValue: '跟踪中',
        componentProps: {
          placeholder: '请选择商机状态',
          options: [
            { fullName: '跟踪中', id: '跟踪中' },
            { fullName: '方案报价中', id: '方案报价中' },
            { fullName: '商务谈判中', id: '商务谈判中' },
            { fullName: '已签', id: '已签' },
            { fullName: '已废弃', id: '已废弃' },
            { fullName: '明年跟踪', id: '明年跟踪' },
          ],
        },
        colProps: { span: 12 },
      },
      {
        field: 'deptMoney',
        label: '软件部金额',
        component: 'InputNumber',
        required: true,
        componentProps: {
          placeholder: '请输入软件部金额',
          min: 0,
          precision: 2,
          style: 'width: 100%',
          addonAfter: '万元',
        },
        rules: [{ required: true, trigger: 'blur', message: '请输入软件部金额' }],
        colProps: { span: 12 },
      },
      {
        field: 'purchaseMoney',
        label: '外采金额',
        component: 'InputNumber',
        required: true,
        componentProps: {
          placeholder: '请输入外采金额',
          min: 0,
          precision: 2,
          style: 'width: 100%',
          addonAfter: '万元',
        },
        rules: [{ required: true, trigger: 'blur', message: '请输入外采金额' }],
        colProps: { span: 12 },
      },
      {
        field: 'profitMoney',
        label: '毛利',
        component: 'InputNumber',
        required: true,
        componentProps: {
          placeholder: '请输入毛利',
          min: 0,
          precision: 2,
          style: 'width: 100%',
          addonAfter: '万元',
        },
        rules: [{ required: true, trigger: 'blur', message: '请输入毛利' }],
        colProps: { span: 12 },
      },
      {
        field: 'projectType',
        label: '项目类型',
        component: 'Select',
        required: true,
        componentProps: {
          placeholder: '请选择项目类型',
          options: [
            { label: '建设', value: '建设' },
            { label: '维护', value: '维护' },
          ],
        },
        rules: [{ required: true, trigger: 'change', message: '请选择项目类型' }],
        colProps: { span: 12 },
      },
      {
        field: 'projectContent',
        label: '项目简介',
        component: 'Input',
        slot: 'projectContent',
        componentProps: { placeholder: '请输入项目简介', maxlength: 2000, showCount: true, rows: 3, type: 'textarea' },
        rules: [{ max: 2000, message: '项目简介最多为2000个字符', trigger: 'blur' }],
        colProps: { span: 24 },
      },
      {
        field: 'evaSignMonth',
        label: '预计落地月份',
        component: 'MonthPicker',
        componentProps: {
          placeholder: '请选择预计落地月份',
          style: 'width: 100%',
          valueFormat: 'YYYY-MM',
        },
        colProps: { span: 12 },
      },
      {
        field: 'checkDate',
        label: '合同审核日期',
        component: 'DatePicker',
        componentProps: { placeholder: '请选择合同审核日期', style: 'width: 100%' },
        colProps: { span: 12 },
      },
      {
        field: 'evaFirstMonth',
        label: '预计首比回款时间',
        component: 'DatePicker',
        componentProps: { placeholder: '请选择预计首比回款时间', style: 'width: 100%' },
        colProps: { span: 12 },
      },
      {
        field: 'evaFirstAmount',
        label: '预计首比回款金额(万元)',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入预计首比回款金额',
          min: 0,
          precision: 2,
          style: 'width: 100%',
          addonAfter: '万元',
        },
        colProps: { span: 12 },
      },
      {
        field: 'evaSecondMonth',
        label: '第二笔回款时间',
        component: 'DatePicker',
        componentProps: { placeholder: '请选择第二笔回款时间', style: 'width: 100%' },
        colProps: { span: 12 },
      },
      {
        field: 'evaSecondAmount',
        label: '二笔回款金额(万元)',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入二笔回款金额',
          min: 0,
          precision: 2,
          style: 'width: 100%',
          addonAfter: '万元',
        },
        colProps: { span: 12 },
      },
      {
        field: 'evaFirstexternalMonth',
        label: '首次外采月份',
        component: 'MonthPicker',
        componentProps: {
          placeholder: '请选择首次外采月份',
          style: 'width: 100%',
          valueFormat: 'YYYY-MM',
        },
        colProps: { span: 12 },
      },
      {
        field: 'evaFirstexternalAmount',
        label: '首次外采金额',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入首次外采金额',
          min: 0,
          precision: 2,
          style: 'width: 100%',
          addonAfter: '万元',
        },
        colProps: { span: 12 },
      },
      {
        field: 'evaSecondexternalMonth',
        label: '二次外采月份',
        component: 'MonthPicker',
        componentProps: {
          placeholder: '请选择二次外采月份',
          style: 'width: 100%',
          valueFormat: 'YYYY-MM',
        },
        colProps: { span: 12 },
      },
      {
        field: 'evaSecondexternalAmount',
        label: '二次外采金额',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入二次外采金额',
          min: 0,
          precision: 2,
          style: 'width: 100%',
          addonAfter: '万元',
        },
        colProps: { span: 12 },
      },
      {
        field: 'yfYbAmount',
        label: '一部金额',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入一部金额',
          min: 0,
          precision: 2,
          style: 'width: 100%',
          addonAfter: '万元',
        },
        colProps: { span: 12 },
      },
      {
        field: 'outYbAmount',
        label: '一部外采',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入一部外采',
          min: 0,
          precision: 2,
          style: 'width: 100%',
          addonAfter: '万元',
        },
        colProps: { span: 12 },
      },
      {
        field: 'yfEbAmount',
        label: '二部金额',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入二部金额',
          min: 0,
          precision: 2,
          style: 'width: 100%',
          addonAfter: '万元',
        },
        colProps: { span: 12 },
      },
      {
        field: 'outEbAmount',
        label: '二部外采',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入二部外采',
          min: 0,
          precision: 2,
          style: 'width: 100%',
          addonAfter: '万元',
        },
        colProps: { span: 12 },
      },
      {
        field: 'yfJfAmount',
        label: '交付金额',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入交付金额',
          min: 0,
          precision: 2,
          style: 'width: 100%',
          addonAfter: '万元',
        },
        colProps: { span: 12 },
      },
      {
        field: 'outJfAmount',
        label: '交付外采',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入交付外采',
          min: 0,
          precision: 2,
          style: 'width: 100%',
          addonAfter: '万元',
        },
        colProps: { span: 12 },
      },
      {
        field: 'yfOtherAmount',
        label: '综合金额',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入综合金额',
          min: 0,
          precision: 2,
          style: 'width: 100%',
          addonAfter: '万元',
        },
        colProps: { span: 12 },
      },
      {
        field: 'outOtherAmount',
        label: '综合外采',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入综合外采',
          min: 0,
          precision: 2,
          style: 'width: 100%',
          addonAfter: '万元',
        },
        colProps: { span: 12 },
      },
    ];

    return baseSchemas;
  });

  // 项目信息表单字段
  const projectFormFields = [
    'projectName',
    'businessNo',
    'projectLevel',
    'custId',
    'marketLinkman',
    'presaleLinkman',
    'projectLeader',
    'deptId',
    'businessTag',
    'status',
    'projectType',
    'projectContent',
    'evaSignMonth',
  ];

  // 合同信息表单字段
  const contractFormFields = [
    'deptMoney',
    'purchaseMoney',
    'profitMoney',
    'checkDate',
    'evaFirstMonth',
    'evaFirstAmount',
    'evaSecondMonth',
    'evaSecondAmount',
  ];

  // 项目外采表单字段
  const outsourceFormFields = [
    'evaFirstexternalMonth',
    'evaFirstexternalAmount',
    'evaSecondexternalMonth',
    'evaSecondexternalAmount',
  ];

  // 合同分配表单字段
  const allocationFormFields = [
    'yfYbAmount',
    'outYbAmount',
    'yfEbAmount',
    'outEbAmount',
    'yfJfAmount',
    'outJfAmount',
    'yfOtherAmount',
    'outOtherAmount',
  ];

  // 注册项目信息表单
  const [registerProjectForm, { resetFields: resetProjectFields, setFieldsValue: setProjectFieldsValue, validate: validateProjectForm }] = useForm({
    labelWidth: 130,
    schemas: formSchemas.value.filter(schema => projectFormFields.includes(schema.field)),
    showActionButtonGroup: false,
    baseColProps: { span: 12 },
  });

  // 注册合同信息表单
  const [registerContractForm, { resetFields: resetContractFields, setFieldsValue: setContractFieldsValue, validate: validateContractForm }] = useForm({
    labelWidth: 130,
    schemas: formSchemas.value.filter(schema => contractFormFields.includes(schema.field)),
    showActionButtonGroup: false,
    baseColProps: { span: 12 },
  });

  // 注册项目外采表单
  const [registerOutsourceForm, { resetFields: resetOutsourceFields, setFieldsValue: setOutsourceFieldsValue, validate: validateOutsourceForm }] = useForm({
    labelWidth: 130,
    schemas: formSchemas.value.filter(schema => outsourceFormFields.includes(schema.field)),
    showActionButtonGroup: false,
    baseColProps: { span: 12 },
  });

  // 注册合同分配表单
  const [registerAllocationForm, { resetFields: resetAllocationFields, setFieldsValue: setAllocationFieldsValue, validate: validateAllocationForm }] = useForm({
    labelWidth: 130,
    schemas: formSchemas.value.filter(schema => allocationFormFields.includes(schema.field)),
    showActionButtonGroup: false,
    baseColProps: { span: 12 },
  });

  // 注册抽屉
  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async data => {
    // 重置所有表单
    resetProjectFields();
    resetContractFields();
    resetOutsourceFields();
    resetAllocationFields();
    setDrawerProps({ confirmLoading: false });
    selectedNavKeys.value = ['project'];

    isUpdate.value = !!data?.isUpdate;
    isCopy.value = !!data?.copyData;

    if (unref(isUpdate)) {
      businessId.value = data.record.id;
      businessType.value = data.record.projType === '提前项目' ? '建设商机' : '维护商机';

      // 获取商机详情
      try {
        const response = await getBusinessInfo(businessId.value);
        if (response.code === 200) {
          const businessDetail = response.data;
          console.log('获取到的商机详情:', businessDetail);
          businessInfo.value = businessDetail;

          // 设置表单值
          const formData = {
            ...businessDetail,
            projType: businessType.value,
          };

          console.log('设置的表单数据:', formData);
          setProjectFieldsValue(formData);
          setContractFieldsValue(formData);
          setOutsourceFieldsValue(formData);
          setAllocationFieldsValue(formData);
        } else {
          createMessage.error(response.msg || '获取商机详情失败');
        }
      } catch (error) {
        console.error('获取商机详情失败:', error);
        createMessage.error('获取商机详情失败');
      }
    } else if (data?.copyData) {
      // 复制模式
      console.log('复制模式，预填充数据:', data.copyData);

      businessType.value = data.copyData.projType === '维护项目' ? '维护商机' : '建设商机';
      businessInfo.value = data.copyData;

      // 转换商机类型显示
      const formData = {
        ...data.copyData,
        projType: businessType.value,
      };

      console.log('设置复制的表单数据:', formData);

      // 设置表单值
      setProjectFieldsValue(formData);
      setContractFieldsValue(formData);
      setOutsourceFieldsValue(formData);
      setAllocationFieldsValue(formData);
    } else {
      businessType.value = '建设商机';
      businessInfo.value = {};
      isCopy.value = false;
    }
  });

  // 提交表单
  async function handleSubmit() {
    try {
      // 验证所有表单
      const projectValues = await validateProjectForm();
      const contractValues = await validateContractForm();
      const outsourceValues = await validateOutsourceForm();
      const allocationValues = await validateAllocationForm();

      // 合并表单数据
      const values = {
        ...projectValues,
        ...contractValues,
        ...outsourceValues,
        ...allocationValues,
      };

      setDrawerProps({ confirmLoading: true });

      // 转换商机类型
      values.projType = values.projType === '建设商机' ? '常规项目' : '维护项目';

      if (unref(isUpdate)) {
        await updateBusiness(businessId.value, values);
        createMessage.success('更新成功');
      } else {
        await createBusiness(values);
        createMessage.success('新增成功');
      }

      closeDrawer();
      emit('reload');
    } catch (error) {
      console.error('表单验证失败或提交出错:', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>

<style lang="less" scoped>
  .business-form-drawer {
    :deep(.ant-drawer-body) {
      padding: 0;
      background: #f5f7fa;
    }

    :deep(.ant-drawer-header) {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-bottom: none;

      .ant-drawer-title {
        color: #fff;
        font-weight: 600;
        font-size: 16px;
      }

      .ant-drawer-close {
        color: rgba(255, 255, 255, 0.8);

        &:hover {
          color: #fff;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
        }
      }
    }

    :deep(.ant-drawer-footer) {
      background: #fff;
      border-top: 1px solid #e8eaec;
      padding: 16px 24px;
    }
  }

  .business-form-container {
    height: 100%;
    padding: 24px;
    overflow-y: auto;
  }

  .overview-card,
  .welcome-card {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8eaec;

    :deep(.ant-card-head) {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-bottom: 1px solid #e8eaec;
      border-radius: 12px 12px 0 0;
      padding: 16px 24px;

      .ant-card-head-title {
        padding: 0;
        font-size: 18px;
        font-weight: 600;
      }
    }

    :deep(.ant-card-body) {
      padding: 24px;
    }
  }

  .overview-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;

    .overview-item {
      .overview-label {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
        font-weight: 500;

        .label-icon {
          margin-right: 6px;
          font-size: 16px;
          color: #1890ff;
        }
      }

      .overview-value {
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;

        &.amount {
          font-size: 18px;
          color: #1890ff;
          font-weight: 700;

          &.external {
            color: #fa8c16;
          }
        }
      }
    }
  }

  .welcome-content {
    .welcome-text {
      display: flex;
      align-items: center;
      padding: 16px 20px;
      color: #666;
      font-size: 14px;
      line-height: 1.6;
      background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
      border-radius: 8px;
      border-left: 4px solid #1890ff;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(24, 144, 255, 0.05) 50%, transparent 70%);
        animation: shimmer 3s infinite;
      }

      .info-icon {
        color: #1890ff;
        margin-right: 12px;
        font-size: 18px;
        animation: pulse 2s infinite;
      }
    }
  }

  @keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }

  @keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
  }

  .card-title {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #2c3e50;

    .title-icon {
      font-size: 18px;
      color: #1890ff;
    }
  }

  .alert-box {
    margin-bottom: 24px;

    :deep(.ant-alert) {
      border-radius: 8px;
      border: none;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    :deep(.ant-alert-success) {
      background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
      border-left: 4px solid #52c41a;
    }

    :deep(.ant-alert-warning) {
      background: linear-gradient(135deg, #fffbe6 0%, #fff7e6 100%);
      border-left: 4px solid #faad14;
    }
  }

  .form-layout {
    height: calc(100% - 200px);
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    position: relative;
  }

  .navigation-sider {
    background: linear-gradient(180deg, #fafbfc 0%, #f5f7fa 100%);
    border-right: 1px solid #e8eaec;
    position: relative;
    height: 100%;
    z-index: 10;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;

    :deep(.ant-layout-sider-children) {
      display: flex;
      flex-direction: column;
      height: 100%;
      overflow-y: auto;
    }

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.1);
      border-radius: 2px;

      &:hover {
        background: rgba(0, 0, 0, 0.2);
      }
    }
  }

  .navigation-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    position: sticky;
    top: 0;
  }

  .navigation-header {
    padding: 24px 20px 20px;
    border-bottom: 1px solid #e8eaec;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: sticky;
    top: 0;
    z-index: 1;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    h4 {
      margin: 0;
      color: #2c3e50;
      font-size: 18px;
      font-weight: 700;
      text-align: center;
      position: relative;
      padding-bottom: 8px;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 3px;
        background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
        border-radius: 2px;
      }
    }
  }

  .navigation-menu {
    flex: 1;
    border: none;
    background: transparent;
    padding: 20px 12px;
    overflow-y: auto;

    :deep(.ant-menu-item) {
      margin: 6px 0;
      border-radius: 12px;
      height: 52px;
      line-height: 52px;
      padding: 0 20px;
      display: flex;
      align-items: center;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      color: #666;
      font-weight: 500;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(24, 144, 255, 0.1), transparent);
        transition: left 0.6s ease;
      }

      &:hover {
        background: rgba(24, 144, 255, 0.08);
        color: #1890ff;
        transform: translateX(4px);
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);

        &::before {
          left: 100%;
        }

        .anticon {
          transform: scale(1.1);
        }
      }

      &.ant-menu-item-selected {
        background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
        color: #fff;
        box-shadow: 0 4px 16px rgba(24, 144, 255, 0.4);
        transform: translateX(8px);

        &::after {
          content: '';
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 24px;
          background: #fff;
          border-radius: 2px 0 0 2px;
        }

        .anticon {
          color: #fff;
          transform: scale(1.1);
        }
      }

      .anticon {
        font-size: 18px;
        margin-right: 12px;
        transition: all 0.3s ease;
      }
    }
  }

  .form-content {
    background: #f5f7fa;
    padding: 0;
    height: 100%;
    position: relative;
    flex: 1;
  }

  .scroll-progress {
    position: absolute;
    top: 0;
    left: 0;
    height: 4px;
    background: linear-gradient(90deg, #1890ff 0%, #40a9ff 50%, #52c41a 100%);
    border-radius: 0 2px 2px 0;
    transition: width 0.3s ease;
    z-index: 100;
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
  }

  .form-scroll-container {
    height: 100%;
    overflow-y: auto;
    padding: 32px 24px;
    scroll-behavior: smooth;
    background: linear-gradient(135deg, #f5f7fa 0%, #f0f2f5 100%);
  }

  .form-module {
    margin-bottom: 32px;
    animation: fadeInUp 0.6s ease-out;

    &:last-child {
      margin-bottom: 32px;
    }
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .module-card {
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8eaec;
    background: #fff;
    transition: all 0.3s ease;
    overflow: hidden;

    &:hover {
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
      transform: translateY(-2px);
    }

    :deep(.ant-card-head) {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-bottom: 1px solid #e8eaec;
      border-radius: 16px 16px 0 0;
      padding: 20px 28px;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 28px;
        right: 28px;
        height: 2px;
        background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
        border-radius: 1px;
      }

      .ant-card-head-title {
        padding: 0;
        font-size: 18px;
        font-weight: 600;
      }
    }

    :deep(.ant-card-body) {
      padding: 28px;
      background: #fff;
    }
  }

  .module-title {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #2c3e50;

    .module-icon {
      font-size: 18px;
      color: #1890ff;
    }
  }

  .form-section {
    background: #fff;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e8eaec;
  }

  :deep(.ant-form) {
    .ant-form-item {
      margin-bottom: 20px;
    }

    .ant-form-item-label > label {
      font-weight: 600;
      color: #2c3e50;
      font-size: 14px;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 0;
        height: 2px;
        background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
        transition: width 0.3s ease;
      }

      &:hover::after {
        width: 100%;
      }
    }

    .ant-input,
    .ant-input-number,
    .ant-select-selector,
    .ant-picker {
      border-radius: 8px;
      border: 2px solid #e8eaec;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      background: #fafbfc;
      font-size: 14px;
      padding: 8px 12px;

      &:hover {
        border-color: #40a9ff;
        background: #fff;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
        transform: translateY(-1px);
      }

      &:focus,
      &.ant-input-focused,
      &.ant-select-focused .ant-select-selector,
      &.ant-picker-focused {
        border-color: #1890ff;
        background: #fff;
        box-shadow: 0 4px 16px rgba(24, 144, 255, 0.2);
        transform: translateY(-2px);
      }
    }

    // 修复输入框双重边框问题
    .ant-input-affix-wrapper {
      border: 2px solid #e8eaec;
      border-radius: 8px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      background: #fafbfc;
      padding: 4px 12px;

      &:hover {
        border-color: #40a9ff;
        background: #fff;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
        transform: translateY(-1px);
      }

      &:focus,
      &.ant-input-affix-wrapper-focused {
        border-color: #1890ff;
        background: #fff;
        box-shadow: 0 4px 16px rgba(24, 144, 255, 0.2);
        transform: translateY(-2px);
      }

      .ant-input {
        border: none;
        box-shadow: none;
        background: transparent;
        padding: 4px 0;

        &:focus {
          border: none;
          box-shadow: none;
        }
      }

      .ant-input-suffix {
        .anticon {
          color: #bfbfbf;
          transition: color 0.3s ease;

          &:hover {
            color: #1890ff;
          }
        }
      }
    }

    .ant-input-number-group-addon {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-color: #d9d9d9;
      color: #666;
      font-weight: 500;
    }

    .ant-select {
      .ant-select-selector {
        min-height: 40px !important;
        padding: 6px 12px !important;
      }

      .ant-select-selection-item {
        color: #2c3e50;
        font-weight: 500;
        line-height: 28px !important;
        padding: 0 !important;
      }

      .ant-select-selection-placeholder {
        line-height: 28px !important;
        color: #bfbfbf;
      }
    }

    // 确保下拉菜单不被遮盖
    .ant-select-dropdown {
      z-index: 9999 !important;
    }

    .ant-picker-dropdown {
      z-index: 9999 !important;
    }
  }

  .enhanced-textarea {
    border-radius: 12px;
    border: 2px solid #e8eaec;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: #fafbfc;
    padding: 12px 16px;
    font-size: 14px;
    line-height: 1.6;
    resize: vertical;
    min-height: 100px;

    &:hover {
      border-color: #40a9ff;
      background: #fff;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
      transform: translateY(-1px);
    }

    &:focus {
      border-color: #1890ff;
      background: #fff;
      box-shadow: 0 4px 16px rgba(24, 144, 255, 0.2);
      transform: translateY(-2px);
      outline: none;
    }

    &::placeholder {
      color: #bfbfbf;
      font-style: italic;
    }
  }

  .mb-4 {
    margin-bottom: 24px;
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .business-form-drawer {
      :deep(.ant-drawer) {
        width: 95% !important;
      }
    }

    .business-form-container {
      padding: 16px;
    }

    .overview-content {
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 16px;
    }

    .form-scroll-container {
      padding: 16px;
    }

    .module-card {
      :deep(.ant-card-body) {
        padding: 20px 16px;
      }
    }
  }

  @media (max-width: 768px) {
    .navigation-sider {
      position: relative;
      width: 100% !important;
      height: auto;
      border-right: none;
      border-bottom: 1px solid #e8eaec;

      :deep(.ant-layout-sider-trigger) {
        display: block;
      }
    }

    .form-content {
      height: auto;
    }

    .form-layout {
      flex-direction: column;
      height: auto;
    }
  }

  // 滚动条美化
  .business-form-container,
  .form-scroll-container {
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  // 标签样式
  :deep(.ant-tag) {
    border-radius: 6px;
    padding: 4px 12px;
    font-weight: 500;
    border: none;
  }
</style>

<!-- 全局样式修复下拉菜单 -->
<style>
/* 确保下拉菜单在最顶层 */
.ant-select-dropdown,
.ant-picker-dropdown,
.ant-cascader-dropdown {
  z-index: 9999 !important;
}

/* 修复下拉菜单选项文字显示 */
.ant-select-item-option-content {
  color: #333 !important;
}

.ant-select-item-option-selected .ant-select-item-option-content {
  color: #1890ff !important;
  font-weight: 600;
}

.ant-select-item-option-active .ant-select-item-option-content {
  color: #1890ff !important;
}
</style>
