package com.xinghuo.project.model.business;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商机视图对象
 */
@Data
@Schema(description = "商机视图对象")
public class ProjBusinessVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 商机编号
     */
    @Schema(description = "商机编号")
    private String businessNo;

    /**
     * 项目名称
     */
    @Schema(description = "项目名称")
    private String projectName;

    /**
     * 客户单位ID
     */
    @Schema(description = "客户单位ID")
    private String custId;

    /**
     * 客户单位名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "客户单位名称")
    private String custName;

    /**
     * 项目等级
     */
    @Schema(description = "项目等级")
    private String projectLevel;

    /**
     * 项目负责人ID
     */
    @Schema(description = "项目负责人ID")
    private String projectLeader;

    /**
     * 项目负责人名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "项目负责人名称")
    private String projectLeaderName;

    /**
     * 商机状态
     */
    @Schema(description = "商机状态")
    private String status;

    /**
     * 项目类型
     */
    @Schema(description = "项目类型")
    private String projType;

    /**
     * 更详细的项目类型
     */
    @Schema(description = "更详细的项目类型")
    private String projectType;

    /**
     * 所属分部ID
     */
    @Schema(description = "所属分部ID")
    private String deptId;

    /**
     * 所属分部名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "所属分部名称")
    private String deptName;

    /**
     * 研发分部ID
     */
    @Schema(description = "研发分部ID")
    private String yfDeptId;

    /**
     * 研发分部名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "研发分部名称")
    private String yfDeptName;

    /**
     * 市场负责人
     */
    @Schema(description = "市场负责人")
    private String marketLinkman;

    /**
     * 售前负责人
     */
    @Schema(description = "售前负责人")
    private String presaleLinkman;

    /**
     * 项目简介
     */
    @Schema(description = "项目简介")
    private String projectContent;

    /**
     * 预计启动日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "预计启动日期")
    private Date startDate;

    /**
     * 预计落地月份
     */
    @Schema(description = "预计落地月份")
    private String evaSignMonth;

    /**
     * 商机标签
     */
    @Schema(description = "商机标签")
    private String businessTag;

    /**
     * 工时填写状态
     */
    @Schema(description = "工时填写状态")
    private String workStatus;

    /**
     * 合同编号
     */
    @Schema(description = "合同编号")
    private String projectNo;

    /**
     * 最后跟踪记录
     */
    @Schema(description = "最后跟踪记录")
    private String lastNote;

    /**
     * 预计首笔回款时间
     */
    @Schema(description = "预计首笔回款时间")
    private String evaFirstMonth;

    /**
     * 预计首笔回款金额
     */
    @Schema(description = "预计首笔回款金额")
    private BigDecimal evaFirstAmount;

    /**
     * 预计二笔回款时间
     */
    @Schema(description = "预计二笔回款时间")
    private String evaSecondMonth;

    /**
     * 预计二笔回款金额
     */
    @Schema(description = "预计二笔回款金额")
    private BigDecimal evaSecondAmount;

    /**
     * 预计首次外采时间
     */
    @Schema(description = "预计首次外采时间")
    private String evaFirstexternalMonth;

    /**
     * 预计首次外采金额
     */
    @Schema(description = "预计首次外采金额")
    private BigDecimal evaFirstexternalAmount;

    /**
     * 预计二次外采时间
     */
    @Schema(description = "预计二次外采时间")
    private String evaSecondexternalMonth;

    /**
     * 预计二次外采金额
     */
    @Schema(description = "预计二次外采金额")
    private BigDecimal evaSecondexternalAmount;

    /**
     * 今年收款比例
     */
    @Schema(description = "今年收款比例")
    private BigDecimal yearMoneyRatio;

    /**
     * 今年收款金额
     */
    @Schema(description = "今年收款金额")
    private BigDecimal yearMoney;

    /**
     * 各部门收入
     */
    @Schema(description = "各部门收入")
    private BigDecimal deptMoney;

    /**
     * 一部收入
     */
    @Schema(description = "一部收入")
    private BigDecimal yfYbAmount;

    /**
     * 二部收入
     */
    @Schema(description = "二部收入")
    private BigDecimal yfEbAmount;

    /**
     * 交付收入
     */
    @Schema(description = "交付收入")
    private BigDecimal yfJfAmount;

    /**
     * 综合收入
     */
    @Schema(description = "综合收入")
    private BigDecimal yfOtherAmount;

    /**
     * 一部外采
     */
    @Schema(description = "一部外采")
    private BigDecimal outYbAmount;

    /**
     * 二部外采
     */
    @Schema(description = "二部外采")
    private BigDecimal outEbAmount;

    /**
     * 交付外采
     */
    @Schema(description = "交付外采")
    private BigDecimal outJfAmount;

    /**
     * 综合外采
     */
    @Schema(description = "综合外采")
    private BigDecimal outOtherAmount;

    /**
     * 预计外采总额
     */
    @Schema(description = "预计外采总额")
    private BigDecimal purchaseMoney;

    /**
     * 预计毛利
     */
    @Schema(description = "预计毛利")
    private BigDecimal profitMoney;

    /**
     * 创建用户ID
     */
    @Schema(description = "创建用户ID")
    private String createUserId;

    /**
     * 创建用户名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "创建用户名称")
    private String createUserName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 最后修改人ID
     */
    @Schema(description = "最后修改人ID")
    private String lastModifyUserId;

    /**
     * 最后修改人名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "最后修改人名称")
    private String lastModifyUserName;

    /**
     * 最后修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "最后修改时间")
    private Date lastModifyTime;

    /**
     * 删除标记
     */
    @Schema(description = "删除标记")
    private Integer deleteMark;
}
