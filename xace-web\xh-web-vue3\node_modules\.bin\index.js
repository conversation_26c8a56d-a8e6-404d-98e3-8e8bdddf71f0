#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/g/v2/pd-xace-v2/xace-web/xh-web-vue3/node_modules/.pnpm/jsbarcode@3.11.5/node_modules/jsbarcode/bin/barcodes/node_modules:/mnt/g/v2/pd-xace-v2/xace-web/xh-web-vue3/node_modules/.pnpm/jsbarcode@3.11.5/node_modules/jsbarcode/bin/node_modules:/mnt/g/v2/pd-xace-v2/xace-web/xh-web-vue3/node_modules/.pnpm/jsbarcode@3.11.5/node_modules/jsbarcode/node_modules:/mnt/g/v2/pd-xace-v2/xace-web/xh-web-vue3/node_modules/.pnpm/jsbarcode@3.11.5/node_modules:/mnt/g/v2/pd-xace-v2/xace-web/xh-web-vue3/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/g/v2/pd-xace-v2/xace-web/xh-web-vue3/node_modules/.pnpm/jsbarcode@3.11.5/node_modules/jsbarcode/bin/barcodes/node_modules:/mnt/g/v2/pd-xace-v2/xace-web/xh-web-vue3/node_modules/.pnpm/jsbarcode@3.11.5/node_modules/jsbarcode/bin/node_modules:/mnt/g/v2/pd-xace-v2/xace-web/xh-web-vue3/node_modules/.pnpm/jsbarcode@3.11.5/node_modules/jsbarcode/node_modules:/mnt/g/v2/pd-xace-v2/xace-web/xh-web-vue3/node_modules/.pnpm/jsbarcode@3.11.5/node_modules:/mnt/g/v2/pd-xace-v2/xace-web/xh-web-vue3/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../jsbarcode/bin/barcodes/index.js" "$@"
else
  exec node  "$basedir/../jsbarcode/bin/barcodes/index.js" "$@"
fi
